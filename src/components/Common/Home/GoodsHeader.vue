<template>
  <div class="goods-header">
    <div
      ref="containerRef"
      class="goods-header__container"
      @scroll="handleScroll"
    >
      <div
        ref="typeRefs"
        v-for="(item, index) in typeList"
        :key="index"
        class="goods-header__item"
        :class="{ 'goods-header__item--active': activeIndex === index }"
        @click="chooseOne(index)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- 自定义滚动条 -->
    <div class="goods-header__scrollbar">
      <div
        class="goods-header__scrollbar-thumb"
        :style="{ transform: `translateX(${scrollbarPosition}px)` }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'

const props = defineProps({
  typeList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['switchTabs'])

const typeRefs = ref([])
const activeIndex = ref(0)
const containerRef = ref(null)
const scrollbarPosition = ref(0)

const chooseOne = (index) => {
  activeIndex.value = index
  emit('switchTabs', props.typeList[index].id)
}

// 处理滚动事件，计算滚动条位置
const handleScroll = () => {
  if (!containerRef.value) return

  const container = containerRef.value
  const scrollLeft = container.scrollLeft
  const scrollWidth = container.scrollWidth
  const clientWidth = container.clientWidth

  // 如果内容宽度小于等于容器宽度，不显示滚动条
  if (scrollWidth <= clientWidth) {
    scrollbarPosition.value = 0
    return
  }

  // 滚动条轨道宽度（60px）
  const trackWidth = 60
  // 滚动条滑块宽度（20px）
  const thumbWidth = 20
  // 可滚动的滚动条距离
  const scrollableThumbDistance = trackWidth - thumbWidth

  // 计算滚动比例
  const scrollRatio = scrollLeft / (scrollWidth - clientWidth)

  // 计算滚动条位置
  scrollbarPosition.value = scrollRatio * scrollableThumbDistance
}

onMounted(() => {
  nextTick(() => {
    if (props.typeList.length > 0) {
      activeIndex.value = 0
    }
  })
})
</script>

<style lang="less" scoped>
.goods-header {
  width: 100vw;
  position: relative;
  padding-bottom: 10px;
  overflow-x: hidden;
  z-index: 98;

  &__container {
    font-size: @font-size-14;
    height: 25px;
    margin-top: 15px;
    display: flex;
    overflow-x: scroll;
    .no-scrollbar();
  }

  &__item {
    white-space: nowrap;
    height: 14px;
    line-height: 14px;
    padding: 0 17px;
    transition: 0.3s;
    color: @text-color-secondary;
    cursor: pointer;

    &:not(:last-child) {
      border-right: 1px solid @divider-color-base;
    }

    &--active {
      position: relative;
      z-index: 2;
      color: @text-color-primary;

      &::after {
        content: "";
        display: block;
        position: absolute;
        left: 50%;
        bottom: -1px;
        width: 56px;
        height: 6px;
        border-radius: @radius-10;
        z-index: -1;
        transform: translateX(-50%);
        background: @gradient-orange-106;
        transition: 0.3s;
      }
    }
  }

  // 自定义滚动条样式
  &__scrollbar {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: rgba(255, 122, 10, 0.2);
    border-radius: 2px;
    overflow: hidden;
  }

  &__scrollbar-thumb {
    width: 20px;
    height: 3px;
    background-color: @theme-color;
    border-radius: 2px;
    transition: transform 0.1s ease;
  }
}
</style>
