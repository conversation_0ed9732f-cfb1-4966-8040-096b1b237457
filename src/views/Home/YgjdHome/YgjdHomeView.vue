<template>
  <div class="ygjd-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <div class="banner-container">
      <transition name="skeleton-fade" mode="out-in">
        <BannerSkeleton v-if="skeletonStates.banner" key="banner-skeleton" />
        <GoodsSwiper v-else-if="headerBannerList.length > 0" key="banner-content" :imageList="headerBannerList"
          mode="landscape" paginationType="fraction" :autoplay="true" :loop="true" @image-click="handleBannerClick" />
      </transition>
    </div>

    <div class="grid-menu-container">
      <transition name="skeleton-fade" mode="out-in">
        <GridMenuSkeleton v-if="skeletonStates.gridMenu" key="grid-skeleton" />
        <IconGrid v-else-if="gridMenuItems.length > 0" key="grid-content" :items="gridMenuItems" :columns="5"
          :show-more="true" :max-items="10" @item-click="handleGridItemClick" @more-click="handleMoreClick" />
      </transition>
    </div>


    <div class="recommend-container">
      <transition name="skeleton-fade" mode="out-in">
        <RecommendSkeleton v-if="skeletonStates.recommend" key="recommend-skeleton" />
        <RecommendView v-else-if="hotGoods.length > 0" key="recommend-content" :hotGoods="hotGoods" />
      </transition>
    </div>


    <van-list :loading="false" :finished="true" :immediate-check="false">
      <div class="waterfall-container">
        <transition name="waterfall-fade" mode="out-in">
          <WaterfallSkeleton v-if="skeletonStates.waterfall" :skeleton-count="6" key="waterfall-skeleton" />
          <Waterfall v-else-if="waterfallGoodsList.length > 0" ref="waterfallWaterfallRef" key="waterfall-waterfall"
            :list="waterfallGoodsList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0"
            :animationDelay="0" :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true" @afterRender="handleWaterfallAfterRender">
            <template #default="{ item }">
              <ProductCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
            </template>
          </Waterfall>
        </transition>
      </div>
      <transition name="fade-up">
        <div class="load-more-container"
          v-if="waterfallGoodsList.length > 0 && !waterfallFinished && waterfallButtonCanShow && !skeletonStates.waterfall && waterfallRenderComplete">
          <WoButton type="text" :disabled="waterfallLoading" @click="handleWaterfallLoadMore" class="load-more-button">
            {{ waterfallLoading ? '加载中...' : '加载更多' }}
          </WoButton>
        </div>
      </transition>
      <transition name="fade-up">
        <div class="no-more-text"
          v-if="waterfallGoodsList.length > 0 && waterfallFinished && !skeletonStates.waterfall">
          <span>没有更多了</span>
        </div>
      </transition>
    </van-list>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { debounce } from 'lodash-es'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import SearchHeader from '@components/Common/SearchHeader.vue'
import GoodsImageSwiper from '@/components/Common/GoodsImageSwiper.vue'
import IconGrid from '@components/Common/Home/IconGrid.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import { fenToYuan } from '@utils/amount.js'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast } from 'vant'
import BannerSkeleton from '@views/Home/components/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@views/Home/components/Skeleton/GridMenuSkeleton.vue'
import WaterfallSkeleton from '@views/Home/components/Skeleton/WaterfallSkeleton.vue'
import RecommendSkeleton from '@views/Home/components/Skeleton/RecommendSkeleton.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'
import ProductCard from '@components/Common/Home/ProductCard.vue'
import { getPartionList } from '@/api/interface/goods'
import GoodsSwiper from "@components/Common/GoodsSwiper.vue";
import RecommendView from "@components/Common/Home/RecommendView.vue";
import { getHotGoods } from '@/api/interface/digitalVillage'

const router = useRouter()
const searchKeyword = ref('')
const headerBannerList = ref([])
const gridMenuItems = ref([])
// 商品列表与分页状态
const waterfallGoodsList = ref([])
const waterfallLoading = ref(false)
const waterfallFinished = ref(false)
const waterfallCurrentPage = ref(1)
const waterfallPageSize = ref(10)
const waterfallIsFirstLoadComplete = ref(false)
const waterfallButtonCanShow = ref(false)
const waterfallRenderComplete = ref(false)

const waterfallWaterfallRef = ref(null)
const skeletonStates = ref({
  banner: true,
  gridMenu: true,
  recommend: true,
  waterfall: true
})

const moduleDataReady = ref({
  banner: false,
  gridMenu: false,
  recommend: false,
  waterfall: false
})


const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}


const breakpoints = ref(getDefaultBreakpoints())
const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}
const handleGoodsClick = (goodsInfo) => {
  if (goodsInfo.goodsId) {
    router.push(`/goodsdetail/${goodsInfo.goodsId}`)
  }
}

const hideSkeletonInOrder = async () => {
  if (moduleDataReady.value.banner && skeletonStates.value.banner) {
    skeletonStates.value.banner = false
    await nextTick()
  }

  if (moduleDataReady.value.gridMenu && skeletonStates.value.gridMenu) {
    skeletonStates.value.gridMenu = false
    await nextTick()
  }

  if (moduleDataReady.value.recommend && skeletonStates.value.recommend) {
    skeletonStates.value.recommend = false
    await nextTick()
  }
}

const getHeaderBannerList = async () => {
  const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 1 })
  if (!err) {
    const bannerData = channelFilterd(json).map(item => ({
      type: 'image',
      url: item.imgUrl,
      alt: item.bannerChName,
      linkUrl: item.url,
    }))
    headerBannerList.value = bannerData
  }

  moduleDataReady.value.banner = true
  await hideSkeletonInOrder()
}

const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 7
  })

  if (!err) {
    if (json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData
    } else {
      gridMenuItems.value = []
    }
  }

  moduleDataReady.value.gridMenu = true
  await hideSkeletonInOrder()
}

const handleSearch = debounce(() => {
  // 搜索逻辑可以在这里实现
}, 300)

const handleBannerClick = ({ item }) => {
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}

const handleGridItemClick = ({ item }) => {
  if (item.url) {
    window.location.href = item.url
  }
}

const handleMoreClick = () => {
}

// 获取限量好物
const getWaterfallList = async (id, sortType, sort, isLoadMore = true) => {
  if (waterfallLoading.value || waterfallFinished.value) return
  waterfallLoading.value = true
  const params = {
    type: 'partion',
    id,
    bizCode: getBizCode('GOODS'),
    page_no: waterfallCurrentPage.value,
    page_size: waterfallPageSize.value,
  }
  if (sortType) {
    params.price_sort = sortType
  }
  const [err, json] = await getGoodsList(params)
  if (!err) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      // sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))



    waterfallGoodsList.value = isLoadMore ? [...waterfallGoodsList.value, ...newItems] : newItems
    if (json.length === 0) {
      waterfallFinished.value = true
    } else {
      waterfallFinished.value = false
    }

    if (isLoadMore) {
      waterfallCurrentPage.value++
    } else {
      waterfallCurrentPage.value = 2
    }


  }
  waterfallLoading.value = false
  waterfallIsFirstLoadComplete.value = true
  moduleDataReady.value.waterfall = true
  waterfallButtonCanShow.value = true
  if (skeletonStates.value.waterfall) skeletonStates.value.waterfall = false
}

const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', '', true)
}

// 新增状态
const typeList = ref([])              // 分区列表
const goodsPoolIdSelected = ref('')   // 当前选中的商品池 ID

// 热门商品推荐数据
const hotGoods = ref([])
const hotlistUpL = ref([])
const hotlistUpR = ref([])
const hotlistDownL = ref([])
const hotlistDownR = ref([])

// 重置并加载商品池商品
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  // 重置列表状态
  waterfallGoodsList.value = []
  waterfallCurrentPage.value = 1
  waterfallFinished.value = false
  // 调用已有瀑布流接口
  getWaterfallList(id, sortType, '', false)
}

// 获取热门商品推荐数据
const initHotGoods = async () => {
  const params = {
    showPage: '1',
    bizCode: getBizCode('GOODS'),
    channel: curChannelBiz.get()
  }

  if (!isUnicom) {
    showLoadingToast()
  }

  const [err, json] = await getHotGoods(params)

  if (!isUnicom) {
    closeToast()
  }

  if (err) {
    console.error('获取热门商品失败:', err.msg)
    // 即使出错也要隐藏骨架屏
    moduleDataReady.value.recommend = true
    await hideSkeletonInOrder()
    return
  }

  const arr = json || []

  // 清空之前的数据
  hotlistUpL.value = []
  hotlistUpR.value = []
  hotlistDownL.value = []
  hotlistDownR.value = []

  // 按照原有逻辑分配数据
  if (arr[0] && arr[1]) {
    hotlistUpL.value.push(arr[0], arr[1])
  }
  if (arr[2] && arr[3]) {
    hotlistUpR.value.push(arr[2], arr[3])
  }
  if (arr[4] && arr[5]) {
    hotlistDownL.value.push(arr[4], arr[5])
  }
  if (arr[6] && arr[7]) {
    hotlistDownR.value.push(arr[6], arr[7])
  }

  // 合并所有数据传给 RecommendView 组件
  hotGoods.value = [...hotlistUpL.value, ...hotlistUpR.value, ...hotlistDownL.value, ...hotlistDownR.value]

  // 标记推荐数据已准备完成
  moduleDataReady.value.recommend = true
  await hideSkeletonInOrder()
}

// 初始化页面：获取分区列表并默认加载第一个分区商品
const initPage = async () => {
  showLoadingToast()
  const [err, json] = await getPartionList({ bizCode: getBizCode('GOODS'), type: 2 })
  closeToast()
  if (err) {
    closeToast()
    return
  }
  // 按 pos 倒序
  typeList.value = json ? json.sort((a, b) => b.pos - a.pos) : []
  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]
    goodsPoolIdSelected.value = recommond.id
    changeGoodsPool(recommond.id, '', '', false)
  }
}

onMounted(() => {
  getHeaderBannerList()
  getIconList()
  initHotGoods()       // 初始化热门商品数据
  initPage()           // 替换原先直接加载瀑布流的调用
})

onUnmounted(() => {
})
</script>

<style scoped lang="less">
.ygjd-home {
  width: 100vw;
  height: 100%;
  overflow: auto;
  background: @bg-color-gray;

  .banner-container {
    margin: @radius-8 @radius-12;
    border-radius: @radius-12;
    overflow: hidden;
  }

  .grid-menu-container {
    //background: @bg-color-white;
    border-radius: @radius-12;
    margin: @radius-8 @radius-12;
  }

  .recommend-container {
    position: relative;
    // min-height: 200px;
  }


  .waterfall-container {
    position: relative;
    //min-height: 500px;
    padding: 0 10px;
    box-sizing: border-box;

    :deep(.vue-waterfall) {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  .waterfall-container {
    position: relative;
    //min-height: 500px;

    :deep(.vue-waterfall) {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  // 瀑布流过渡动画
  .waterfall-fade-enter-active,
  .waterfall-fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .waterfall-fade-enter-from,
  .waterfall-fade-leave-to {
    opacity: 0;
  }

  .waterfall-fade-enter-to,
  .waterfall-fade-leave-from {
    opacity: 1;
  }

  // 骨架屏过渡动画
  .skeleton-fade-enter-active,
  .skeleton-fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .skeleton-fade-enter-from,
  .skeleton-fade-leave-to {
    opacity: 0;
  }

  .skeleton-fade-enter-to,
  .skeleton-fade-leave-from {
    opacity: 1;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    color: @text-color-tertiary;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;

    .horizontal-scroll-container & {
      position: static;
      transform: none;
      padding: 60px 0;
    }
  }

  .load-more-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .load-more-button {
      min-width: 120px;
      height: @button-height-36;
      font-size: @font-size-14;

      &.wo-button-text {
        background-color: transparent;
        border-radius: @radius-18;

        &:active {
          opacity: @opacity-07;
        }

        &.wo-button-disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:active {
            transform: none;
          }
        }
      }
    }
  }

  .no-more-text {
    padding: 20px 0 16px;
    text-align: center;

    span {
      font-size: @font-size-14;
      color: @text-color-tertiary;
    }
  }
}
</style>
