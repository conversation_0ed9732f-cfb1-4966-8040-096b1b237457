<template>
  <div class="goods-header-skeleton">
    <div class="skeleton-header-container">
      <div v-for="i in 4" :key="i" class="skeleton-header-item" :class="{ 'skeleton-header-item--active': i === 1 }">
        <div class="skeleton-text"></div>
        <div v-if="i === 1" class="skeleton-active-indicator"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.goods-header-skeleton {
  // 与 GoodsHeader 组件样式保持一致
  width: 100vw;
  position: relative;
  padding-bottom: 10px;
  overflow-x: hidden;
  z-index: 98;

  .skeleton-header-container {
    // 与 GoodsHeader 的 __container 样式保持一致
    font-size: 14px;
    height: 25px;
    margin-top: 15px;
    display: flex;
    overflow-x: scroll;
    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .skeleton-header-item {
      position: relative;
      display: flex;
      align-items: center;
      // 与 GoodsHeader 的 __item 样式保持一致
      white-space: nowrap;
      height: 14px;
      line-height: 14px;
      padding: 0 17px;
      
      // 分隔线（除了最后一个）
      &:not(:last-child)::before {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 10px;
        background-color: #e8e8e8;
      }

      .skeleton-text {
        .skeleton-base();
        // 模拟标签文字的宽度
        width: 48px;
        height: 12px;
        border-radius: 2px;
      }

      // 激活状态的下划线指示器
      .skeleton-active-indicator {
        position: absolute;
        left: 50%;
        bottom: -1px;
        width: 56px;
        height: 6px;
        border-radius: 10px;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        // 不需要动画，保持静态橙色
        animation: none;
      }

      // 激活状态的文字样式
      &--active {
        .skeleton-text {
          // 激活状态的文字稍微宽一点
          width: 52px;
          // 稍微深一点的颜色表示激活状态
          background: linear-gradient(90deg, #e8e8e8 25%, #d0d0d0 50%, #e8e8e8 75%);
          background-size: 200px 100%;
          animation: skeleton-loading 1.2s ease-in-out infinite;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 375px) {
  .goods-header-skeleton {
    .skeleton-header-container {
      .skeleton-header-item {
        // 移动端稍微减少padding
        padding: 0 15px;

        .skeleton-text {
          // 移动端文字稍微小一点
          width: 42px;
          height: 11px;
        }

        .skeleton-active-indicator {
          // 移动端指示器稍微小一点
          width: 50px;
          height: 5px;
        }

        &--active {
          .skeleton-text {
            width: 46px;
          }
        }
      }
    }
  }
}
</style>
